package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.ProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacySettingsScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    profileViewModel: ProfileViewModel = viewModel()
) {
    val userProfile by profileViewModel.userProfile.collectAsState()
    val uiState by profileViewModel.uiState.collectAsState()

    var shareDataForResearch by remember { mutableStateOf(false) }
    var allowAnalytics by remember { mutableStateOf(true) }
    var biometricLock by remember { mutableStateOf(false) }
    var dataRetentionMonths by remember { mutableStateOf(24) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    // Initialize with current profile data
    LaunchedEffect(userProfile) {
        userProfile?.let { profile ->
            shareDataForResearch = profile.privacy.shareDataForResearch
            allowAnalytics = profile.privacy.allowAnalytics
            biometricLock = profile.privacy.biometricLock
            dataRetentionMonths = profile.privacy.dataRetentionMonths
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = RosePink40
                )
            }
            Text(
                text = "Privacy Settings",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.weight(1f)
            )
        }

        // Data Privacy Card
        PrivacyCard(
            title = "Data Privacy",
            items = listOf(
                PrivacyItem(
                    title = "Share Data for Research",
                    description = "Help improve women's health research (anonymized data only)",
                    icon = Icons.Default.Science,
                    iconColor = Color(0xFF4CAF50),
                    isEnabled = shareDataForResearch,
                    onToggle = { shareDataForResearch = it }
                ),
                PrivacyItem(
                    title = "Analytics & Insights",
                    description = "Allow app usage analytics to improve your experience",
                    icon = Icons.Default.Analytics,
                    iconColor = Color(0xFF2196F3),
                    isEnabled = allowAnalytics,
                    onToggle = { allowAnalytics = it }
                )
            )
        )

        // Security Card
        PrivacyCard(
            title = "Security",
            items = listOf(
                PrivacyItem(
                    title = "Biometric Lock",
                    description = "Require fingerprint or face unlock to access the app",
                    icon = Icons.Default.Fingerprint,
                    iconColor = RosePink40,
                    isEnabled = biometricLock,
                    onToggle = { biometricLock = it }
                )
            )
        )

        // Data Retention Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Data Retention",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "Data Retention",
                        tint = Color(0xFF9C27B0),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Keep data for ${dataRetentionMonths} months",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "Automatically delete old data after this period",
                            fontSize = 14.sp,
                            color = DarkGray
                        )
                    }
                }

                Slider(
                    value = dataRetentionMonths.toFloat(),
                    onValueChange = { dataRetentionMonths = it.toInt() },
                    valueRange = 6f..60f,
                    steps = 17, // 6, 12, 18, 24, 30, 36, 42, 48, 54, 60
                    colors = SliderDefaults.colors(
                        thumbColor = RosePink40,
                        activeTrackColor = RosePink40
                    )
                )

                Text(
                    text = "Range: 6 months to 5 years",
                    fontSize = 12.sp,
                    color = DarkGray
                )
            }
        }

        // Account Management Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Account Management",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                // Download Data Button
                OutlinedButton(
                    onClick = { /* Navigate to data export */ },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF2196F3)
                    ),
                    border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFF2196F3))
                ) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = "Download",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Download My Data")
                }

                // Delete Account Button
                OutlinedButton(
                    onClick = { showDeleteDialog = true },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = ErrorRed
                    ),
                    border = androidx.compose.foundation.BorderStroke(1.dp, ErrorRed)
                ) {
                    Icon(
                        imageVector = Icons.Default.DeleteForever,
                        contentDescription = "Delete",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Delete Account")
                }
            }
        }

        // Privacy Notice Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFE3F2FD)
            )
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.Top
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = "Info",
                    tint = Color(0xFF1976D2),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = "Your Privacy Matters",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0D47A1)
                    )
                    Text(
                        text = "We are committed to protecting your personal health data. All data is encrypted and stored securely. You have full control over your privacy settings.",
                        fontSize = 14.sp,
                        color = Color(0xFF1565C0)
                    )
                }
            }
        }

        // Error Message
        uiState.errorMessage?.let { errorMessage ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = ErrorRed.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = errorMessage,
                    color = ErrorRed,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        // Save Button
        Button(
            onClick = {
                val currentProfile = userProfile ?: return@Button
                val updatedProfile = currentProfile.copy(
                    privacy = currentProfile.privacy.copy(
                        shareDataForResearch = shareDataForResearch,
                        allowAnalytics = allowAnalytics,
                        biometricLock = biometricLock,
                        dataRetentionMonths = dataRetentionMonths
                    )
                )
                profileViewModel.updateUserProfile(updatedProfile)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = RosePink40
            ),
            shape = RoundedCornerShape(12.dp),
            enabled = !uiState.isLoading
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "Save",
                        tint = Color.White
                    )
                    Text(
                        text = "Save Settings",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        // Bottom spacing
        Spacer(modifier = Modifier.height(80.dp))
    }

    // Delete Account Dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("Delete Account") },
            text = { 
                Text("Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        // TODO: Implement account deletion
                    }
                ) {
                    Text("Delete", color = ErrorRed)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun PrivacyCard(
    title: String,
    items: List<PrivacyItem>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            items.forEach { item ->
                PrivacyItemRow(item = item)
            }
        }
    }
}

@Composable
private fun PrivacyItemRow(
    item: PrivacyItem,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.title,
            tint = item.iconColor,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(16.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = item.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = item.description,
                fontSize = 14.sp,
                color = DarkGray
            )
        }
        Switch(
            checked = item.isEnabled,
            onCheckedChange = item.onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = item.iconColor
            )
        )
    }
}

data class PrivacyItem(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val iconColor: Color,
    val isEnabled: Boolean,
    val onToggle: (Boolean) -> Unit
)
