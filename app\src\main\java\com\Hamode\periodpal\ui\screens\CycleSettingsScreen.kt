package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.ProfileViewModel
import java.time.LocalDate

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CycleSettingsScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    profileViewModel: ProfileViewModel = viewModel()
) {
    val userProfile by profileViewModel.userProfile.collectAsState()
    val uiState by profileViewModel.uiState.collectAsState()

    var averageCycleLength by remember { mutableStateOf("28") }
    var averagePeriodLength by remember { mutableStateOf("5") }
    var lastPeriodStart by remember { mutableStateOf("") }
    var enablePeriodReminders by remember { mutableStateOf(true) }
    var enableOvulationReminders by remember { mutableStateOf(true) }
    var reminderTime by remember { mutableStateOf("09:00") }

    // Initialize with current profile data
    LaunchedEffect(userProfile) {
        userProfile?.let { profile ->
            averageCycleLength = profile.averageCycleLength.toString()
            averagePeriodLength = profile.averagePeriodLength.toString()
            lastPeriodStart = profile.lastPeriodStart?.toString() ?: ""
            enablePeriodReminders = profile.notifications.periodReminders
            enableOvulationReminders = profile.notifications.ovulationReminders
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = RosePink40
                )
            }
            Text(
                text = "Cycle Settings",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.weight(1f)
            )
        }

        // Cycle Information Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Cycle Information",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                // Average Cycle Length
                OutlinedTextField(
                    value = averageCycleLength,
                    onValueChange = { averageCycleLength = it },
                    label = { Text("Average Cycle Length (days)") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = "Cycle Length",
                            tint = RosePink40
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Average Period Length
                OutlinedTextField(
                    value = averagePeriodLength,
                    onValueChange = { averagePeriodLength = it },
                    label = { Text("Average Period Length (days)") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Bloodtype,
                            contentDescription = "Period Length",
                            tint = PeriodRed
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Last Period Start Date
                OutlinedTextField(
                    value = lastPeriodStart,
                    onValueChange = { lastPeriodStart = it },
                    label = { Text("Last Period Start Date (YYYY-MM-DD)") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Event,
                            contentDescription = "Last Period",
                            tint = RosePink40
                        )
                    },
                    placeholder = { Text("2024-01-01") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )
            }
        }

        // Reminder Settings Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Reminder Settings",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                // Period Reminders
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = "Period Reminders",
                        tint = RosePink40,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Period Reminders",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "Get notified before your period starts",
                            fontSize = 14.sp,
                            color = DarkGray
                        )
                    }
                    Switch(
                        checked = enablePeriodReminders,
                        onCheckedChange = { enablePeriodReminders = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = RosePink40
                        )
                    )
                }

                // Ovulation Reminders
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = "Ovulation Reminders",
                        tint = FertileGreen,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Ovulation Reminders",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "Get notified during fertile window",
                            fontSize = 14.sp,
                            color = DarkGray
                        )
                    }
                    Switch(
                        checked = enableOvulationReminders,
                        onCheckedChange = { enableOvulationReminders = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = FertileGreen
                        )
                    )
                }

                // Reminder Time
                OutlinedTextField(
                    value = reminderTime,
                    onValueChange = { reminderTime = it },
                    label = { Text("Daily Reminder Time") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = "Reminder Time",
                            tint = RosePink40
                        )
                    },
                    placeholder = { Text("09:00") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )
            }
        }

        // Error Message
        uiState.errorMessage?.let { errorMessage ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = ErrorRed.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = errorMessage,
                    color = ErrorRed,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        // Save Button
        Button(
            onClick = {
                val currentProfile = userProfile ?: return@Button
                val updatedProfile = currentProfile.copy(
                    averageCycleLength = averageCycleLength.toIntOrNull() ?: 28,
                    averagePeriodLength = averagePeriodLength.toIntOrNull() ?: 5,
                    lastPeriodStart = if (lastPeriodStart.isNotBlank()) {
                        try {
                            LocalDate.parse(lastPeriodStart)
                        } catch (e: Exception) {
                            null
                        }
                    } else null,
                    notifications = currentProfile.notifications.copy(
                        periodReminders = enablePeriodReminders,
                        ovulationReminders = enableOvulationReminders
                    )
                )
                profileViewModel.updateUserProfile(updatedProfile)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = RosePink40
            ),
            shape = RoundedCornerShape(12.dp),
            enabled = !uiState.isLoading
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "Save",
                        tint = Color.White
                    )
                    Text(
                        text = "Save Settings",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        // Bottom spacing
        Spacer(modifier = Modifier.height(80.dp))
    }
}
