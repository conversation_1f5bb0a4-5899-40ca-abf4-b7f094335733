package com.Hamode.periodpal

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.ui.theme.PeriodPalTheme
import com.Hamode.periodpal.ui.viewmodel.AuthViewModel
import com.Hamode.periodpal.utils.FirebaseTestUtils
import com.google.firebase.FirebaseApp

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize Firebase
        FirebaseApp.initializeApp(this)

        // Test Firebase connection
        FirebaseTestUtils.testFirebaseConnection()
        FirebaseTestUtils.testFirebaseRepository()

        enableEdgeToEdge()
        setContent {
            PeriodPalTheme {
                PeriodPalApp()
            }
        }
    }
}

@Composable
fun PeriodPalApp() {
    val authViewModel: AuthViewModel = viewModel()
    val authState by authViewModel.authState.collectAsState()

    when (authState) {
        is com.Hamode.periodpal.ui.viewmodel.AuthState.Loading -> {
            LoadingScreen()
        }
        is com.Hamode.periodpal.ui.viewmodel.AuthState.Unauthenticated -> {
            com.Hamode.periodpal.ui.screens.AuthScreen(
                onLoginSuccess = { /* Auth handled by ViewModel */ },
                onSignUpSuccess = { /* Auth handled by ViewModel */ },
                authViewModel = authViewModel
            )
        }
        is com.Hamode.periodpal.ui.viewmodel.AuthState.Authenticated -> {
            MainAppContent(authViewModel = authViewModel)
        }
    }
}

@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.White,
                        com.Hamode.periodpal.ui.theme.SoftPink80.copy(alpha = 0.3f)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "🌸",
                fontSize = 64.sp
            )
            CircularProgressIndicator(
                color = com.Hamode.periodpal.ui.theme.RosePink40
            )
            Text(
                text = "Loading PeriodPal...",
                color = com.Hamode.periodpal.ui.theme.DarkGray,
                fontSize = 16.sp
            )
        }
    }
}

@Composable
fun MainAppContent(authViewModel: AuthViewModel) {
    var currentDestination by remember { mutableStateOf(com.Hamode.periodpal.ui.navigation.NavigationDestination.HOME) }
    var currentSecondaryScreen by remember { mutableStateOf<com.Hamode.periodpal.ui.navigation.SecondaryDestination?>(null) }

    if (currentSecondaryScreen != null) {
        when (currentSecondaryScreen) {
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.EDIT_PROFILE -> {
                com.Hamode.periodpal.ui.screens.EditProfileScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.CYCLE_SETTINGS -> {
                com.Hamode.periodpal.ui.screens.CycleSettingsScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.NOTIFICATIONS -> {
                com.Hamode.periodpal.ui.screens.NotificationsScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.DATA_EXPORT -> {
                com.Hamode.periodpal.ui.screens.DataExportScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.PRIVACY_SETTINGS -> {
                com.Hamode.periodpal.ui.screens.PrivacySettingsScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.EMERGENCY_CONTACTS -> {
                com.Hamode.periodpal.ui.screens.EmergencyContactsScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.HELP_FAQ -> {
                com.Hamode.periodpal.ui.screens.HelpFAQScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.CONTACT_SUPPORT -> {
                com.Hamode.periodpal.ui.screens.ContactSupportScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            com.Hamode.periodpal.ui.navigation.SecondaryDestination.ABOUT -> {
                com.Hamode.periodpal.ui.screens.AboutScreen(
                    onNavigateBack = { currentSecondaryScreen = null }
                )
            }
            else -> {
                // Fallback to main content
                currentSecondaryScreen = null
            }
        }
    } else {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            bottomBar = {
                com.Hamode.periodpal.ui.components.PeriodPalBottomNavigation(
                    currentDestination = currentDestination,
                    onNavigate = { destination ->
                        currentDestination = destination
                    }
                )
            }
        ) { innerPadding ->
        when (currentDestination) {
            com.Hamode.periodpal.ui.navigation.NavigationDestination.HOME -> {
                com.Hamode.periodpal.ui.screens.HomeScreen(
                    onNavigateToCalendar = {
                        currentDestination = com.Hamode.periodpal.ui.navigation.NavigationDestination.CALENDAR
                    },
                    onNavigateToLogs = {
                        currentDestination = com.Hamode.periodpal.ui.navigation.NavigationDestination.HEALTH_LOGS
                    },
                    onNavigateToInsights = {
                        currentDestination = com.Hamode.periodpal.ui.navigation.NavigationDestination.INSIGHTS
                    },
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.CALENDAR -> {
                com.Hamode.periodpal.ui.screens.CalendarScreen(
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.HEALTH_LOGS -> {
                com.Hamode.periodpal.ui.screens.HealthLogsScreen(
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.INSIGHTS -> {
                com.Hamode.periodpal.ui.screens.HealthInsightsScreen(
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.PROFILE -> {
                com.Hamode.periodpal.ui.screens.ProfileScreen(
                    onSignOut = { authViewModel.signOut() },
                    onNavigateToSecondaryScreen = { screen -> currentSecondaryScreen = screen },
                    modifier = Modifier.padding(innerPadding)
                )
            }
        }
    }
    }
}

@Composable
fun PlaceholderScreen(
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Column(
            horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
            verticalArrangement = androidx.compose.foundation.layout.Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium,
                color = com.Hamode.periodpal.ui.theme.RosePink40
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodyLarge,
                color = com.Hamode.periodpal.ui.theme.DarkGray,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            Text(
                text = "Coming Soon! 🌸",
                style = MaterialTheme.typography.bodyMedium,
                color = com.Hamode.periodpal.ui.theme.RosePink40
            )
        }
    }
}