package com.Hamode.periodpal.utils

import android.content.Context
import android.os.Bundle
import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase

object AnalyticsUtils {
    private const val TAG = "AnalyticsUtils"
    
    private var analytics: FirebaseAnalytics? = null
    
    fun initialize(context: Context) {
        try {
            analytics = Firebase.analytics

            // Enable debug mode for testing (remove in production)
            analytics?.setAnalyticsCollectionEnabled(true)

            Log.d(TAG, "Firebase Analytics initialized successfully")
            Log.d(TAG, "Analytics collection enabled: true")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Firebase Analytics", e)
        }
    }
    
    // User Events
    fun logUserSignUp(method: String) {
        logEvent(FirebaseAnalytics.Event.SIGN_UP) {
            putString(FirebaseAnalytics.Param.METHOD, method)
        }
    }
    
    fun logUserLogin(method: String) {
        logEvent(FirebaseAnalytics.Event.LOGIN) {
            putString(FirebaseAnalytics.Param.METHOD, method)
        }
    }
    
    // Period Tracking Events
    fun logPeriodStart() {
        logEvent("period_start") {
            putString("event_category", "period_tracking")
        }
    }
    
    fun logPeriodEnd(durationDays: Int) {
        logEvent("period_end") {
            putString("event_category", "period_tracking")
            putInt("duration_days", durationDays)
        }
    }
    
    fun logFlowIntensityChange(intensity: String) {
        logEvent("flow_intensity_change") {
            putString("event_category", "period_tracking")
            putString("intensity", intensity)
        }
    }
    
    // Symptom Tracking Events
    fun logSymptomLogged(symptom: String) {
        logEvent("symptom_logged") {
            putString("event_category", "symptom_tracking")
            putString("symptom_type", symptom)
        }
    }
    
    fun logPainLevelLogged(painLevel: Int) {
        logEvent("pain_level_logged") {
            putString("event_category", "symptom_tracking")
            putInt("pain_level", painLevel)
        }
    }
    
    fun logMoodLogged(mood: String) {
        logEvent("mood_logged") {
            putString("event_category", "mood_tracking")
            putString("mood_type", mood)
        }
    }
    
    // App Usage Events
    fun logScreenView(screenName: String) {
        logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
            putString(FirebaseAnalytics.Param.SCREEN_CLASS, screenName)
        }
    }
    
    fun logCalendarView(viewType: String) {
        logEvent("calendar_view") {
            putString("event_category", "navigation")
            putString("view_type", viewType)
        }
    }
    
    fun logInsightsView() {
        logEvent("insights_view") {
            putString("event_category", "navigation")
        }
    }
    
    // Settings Events
    fun logSettingsChange(settingName: String, value: String) {
        logEvent("settings_change") {
            putString("event_category", "settings")
            putString("setting_name", settingName)
            putString("setting_value", value)
        }
    }
    
    fun logNotificationSettingsChange(enabled: Boolean) {
        logEvent("notification_settings_change") {
            putString("event_category", "settings")
            putBoolean("notifications_enabled", enabled)
        }
    }
    
    // Data Export Events
    fun logDataExport() {
        logEvent("data_export") {
            putString("event_category", "data_management")
        }
    }
    
    // Profile Events
    fun logProfileUpdate(field: String) {
        logEvent("profile_update") {
            putString("event_category", "profile")
            putString("updated_field", field)
        }
    }
    
    fun logAvatarChange(avatarId: String) {
        logEvent("avatar_change") {
            putString("event_category", "profile")
            putString("avatar_id", avatarId)
        }
    }
    
    // Cycle Statistics Events
    fun logCycleStatisticsView() {
        logEvent("cycle_statistics_view") {
            putString("event_category", "analytics")
        }
    }
    
    // Error Events
    fun logError(errorType: String, errorMessage: String) {
        logEvent("app_error") {
            putString("error_type", errorType)
            putString("error_message", errorMessage)
        }
    }
    
    // Privacy Events
    fun logPrivacySettingsChange(settingName: String, enabled: Boolean) {
        logEvent("privacy_settings_change") {
            putString("event_category", "privacy")
            putString("setting_name", settingName)
            putBoolean("enabled", enabled)
        }
    }
    
    // Helper function to log events with proper error handling
    private fun logEvent(eventName: String, parameters: Bundle.() -> Unit = {}) {
        try {
            analytics?.let { firebaseAnalytics ->
                val bundle = Bundle().apply(parameters)
                firebaseAnalytics.logEvent(eventName, bundle)
                Log.d(TAG, "✅ Analytics event logged: $eventName with ${bundle.size()} parameters")

                // Log parameters for debugging
                bundle.keySet().forEach { key ->
                    val value = bundle.get(key)
                    Log.d(TAG, "  Parameter: $key = $value")
                }
            } ?: run {
                Log.w(TAG, "❌ Analytics not initialized, event not logged: $eventName")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to log analytics event: $eventName", e)
        }
    }
    
    // Set user properties
    fun setUserProperty(name: String, value: String) {
        try {
            analytics?.setUserProperty(name, value)
            Log.d(TAG, "User property set: $name = $value")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set user property: $name", e)
        }
    }
    
    // Set user ID
    fun setUserId(userId: String) {
        try {
            analytics?.setUserId(userId)
            Log.d(TAG, "User ID set: $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set user ID", e)
        }
    }
    
    // Enable/disable analytics collection
    fun setAnalyticsCollectionEnabled(enabled: Boolean) {
        try {
            analytics?.setAnalyticsCollectionEnabled(enabled)
            Log.d(TAG, "Analytics collection enabled: $enabled")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set analytics collection enabled", e)
        }
    }

    // Check if analytics is properly initialized
    fun isAnalyticsInitialized(): Boolean {
        val initialized = analytics != null
        Log.d(TAG, "Analytics initialized: $initialized")
        return initialized
    }

    // Test analytics with a simple event
    fun testAnalytics() {
        Log.d(TAG, "=== Testing Firebase Analytics ===")
        Log.d(TAG, "Analytics instance: ${analytics?.javaClass?.simpleName ?: "null"}")

        if (isAnalyticsInitialized()) {
            logEvent("analytics_test") {
                putString("test_type", "initialization_test")
                putLong("timestamp", System.currentTimeMillis())
                putString("app_version", "1.0.0")
            }
            Log.d(TAG, "✅ Test event sent successfully")
        } else {
            Log.e(TAG, "❌ Analytics not initialized - cannot send test event")
        }
    }
}
