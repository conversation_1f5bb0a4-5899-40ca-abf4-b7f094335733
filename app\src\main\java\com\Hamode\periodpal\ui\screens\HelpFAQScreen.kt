package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.ui.theme.*

data class FAQItem(
    val id: String,
    val question: String,
    val answer: String,
    val category: String,
    val isExpanded: Boolean = false
)

data class HelpCategory(
    val name: String,
    val icon: ImageVector,
    val color: Color,
    val items: List<FAQItem>
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HelpFAQScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var searchQuery by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf("All") }
    var expandedItems by remember { mutableStateOf(setOf<String>()) }

    val helpCategories = remember {
        listOf(
            HelpCategory(
                name = "Getting Started",
                icon = Icons.Default.PlayArrow,
                color = FertileGreen,
                items = listOf(
                    FAQItem(
                        id = "1",
                        question = "How do I set up my cycle tracking?",
                        answer = "Go to Profile > Cycle Settings to enter your average cycle length, period length, and last period start date. This helps PeriodPal provide accurate predictions.",
                        category = "Getting Started"
                    ),
                    FAQItem(
                        id = "2",
                        question = "What information should I log daily?",
                        answer = "Log your flow intensity, symptoms, mood, and any notes. The more data you provide, the better insights PeriodPal can give you about your cycle patterns.",
                        category = "Getting Started"
                    )
                )
            ),
            HelpCategory(
                name = "Cycle Tracking",
                icon = Icons.Default.CalendarToday,
                color = RosePink40,
                items = listOf(
                    FAQItem(
                        id = "3",
                        question = "How accurate are the period predictions?",
                        answer = "Predictions become more accurate as you log more cycles. After 3-6 months of consistent logging, predictions are typically accurate within 1-2 days.",
                        category = "Cycle Tracking"
                    ),
                    FAQItem(
                        id = "4",
                        question = "What if my cycle is irregular?",
                        answer = "PeriodPal adapts to irregular cycles. Keep logging your periods and symptoms, and the app will learn your unique patterns over time.",
                        category = "Cycle Tracking"
                    )
                )
            ),
            HelpCategory(
                name = "Privacy & Data",
                icon = Icons.Default.Security,
                color = Color(0xFF2196F3),
                items = listOf(
                    FAQItem(
                        id = "5",
                        question = "Is my data secure?",
                        answer = "Yes, all your data is encrypted and stored securely. We never share your personal health information without your explicit consent.",
                        category = "Privacy & Data"
                    ),
                    FAQItem(
                        id = "6",
                        question = "Can I export my data?",
                        answer = "Yes, go to Profile > Data Export to download your data in JSON or CSV format. This is useful for sharing with healthcare providers.",
                        category = "Privacy & Data"
                    )
                )
            ),
            HelpCategory(
                name = "Troubleshooting",
                icon = Icons.Default.Build,
                color = Color(0xFFFF9800),
                items = listOf(
                    FAQItem(
                        id = "7",
                        question = "The app is not sending notifications",
                        answer = "Check your device notification settings and ensure PeriodPal has permission to send notifications. Also verify your notification preferences in the app settings.",
                        category = "Troubleshooting"
                    ),
                    FAQItem(
                        id = "8",
                        question = "I can't sync my data",
                        answer = "Make sure you have a stable internet connection and are logged into your account. Try refreshing the app or logging out and back in.",
                        category = "Troubleshooting"
                    )
                )
            )
        )
    }

    val allFAQs = helpCategories.flatMap { it.items }
    val filteredFAQs = allFAQs.filter { faq ->
        (selectedCategory == "All" || faq.category == selectedCategory) &&
        (searchQuery.isEmpty() || faq.question.contains(searchQuery, ignoreCase = true) || 
         faq.answer.contains(searchQuery, ignoreCase = true))
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = RosePink40
                )
            }
            Text(
                text = "Help & FAQ",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.weight(1f)
            )
        }

        // Search Bar
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            label = { Text("Search help topics...") },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search",
                    tint = RosePink40
                )
            },
            trailingIcon = {
                if (searchQuery.isNotEmpty()) {
                    IconButton(onClick = { searchQuery = "" }) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "Clear",
                            tint = DarkGray
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = RosePink40,
                focusedLabelColor = RosePink40
            )
        )

        // Category Filters
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FilterChip(
                        onClick = { selectedCategory = "All" },
                        label = { Text("All") },
                        selected = selectedCategory == "All",
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = RosePink40,
                            selectedLabelColor = Color.White
                        )
                    )
                    helpCategories.forEach { category ->
                        FilterChip(
                            onClick = { selectedCategory = category.name },
                            label = { Text(category.name) },
                            selected = selectedCategory == category.name,
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = category.color,
                                selectedLabelColor = Color.White
                            )
                        )
                    }
                }
            }

            // Quick Actions
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(20.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "Quick Actions",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.onSurface
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            QuickActionButton(
                                text = "Contact Support",
                                icon = Icons.Default.Support,
                                color = Color(0xFF4CAF50),
                                onClick = { /* Navigate to contact support */ },
                                modifier = Modifier.weight(1f)
                            )
                            QuickActionButton(
                                text = "Video Tutorials",
                                icon = Icons.Default.PlayCircle,
                                color = Color(0xFF2196F3),
                                onClick = { /* Open tutorials */ },
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }

            // FAQ Items
            items(filteredFAQs) { faq ->
                FAQCard(
                    faq = faq,
                    isExpanded = expandedItems.contains(faq.id),
                    onToggleExpanded = {
                        expandedItems = if (expandedItems.contains(faq.id)) {
                            expandedItems - faq.id
                        } else {
                            expandedItems + faq.id
                        }
                    }
                )
            }

            // No Results
            if (filteredFAQs.isEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.SearchOff,
                                contentDescription = "No Results",
                                tint = DarkGray,
                                modifier = Modifier.size(48.dp)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "No results found",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Medium,
                                color = DarkGray
                            )
                            Text(
                                text = "Try adjusting your search or contact support",
                                fontSize = 14.sp,
                                color = DarkGray
                            )
                        }
                    }
                }
            }

            // Bottom spacing
            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Composable
private fun FAQCard(
    faq: FAQItem,
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onToggleExpanded() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = faq.question,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(1f)
                )
                Icon(
                    imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (isExpanded) "Collapse" else "Expand",
                    tint = RosePink40
                )
            }

            if (isExpanded) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = faq.answer,
                    fontSize = 14.sp,
                    color = DarkGray,
                    lineHeight = 20.sp
                )
            }
        }
    }
}

@Composable
private fun QuickActionButton(
    text: String,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier,
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = color
        ),
        border = androidx.compose.foundation.BorderStroke(1.dp, color)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                modifier = Modifier.size(20.dp)
            )
            Text(
                text = text,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
