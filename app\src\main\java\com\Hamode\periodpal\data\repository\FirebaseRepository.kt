package com.Hamode.periodpal.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.Hamode.periodpal.data.models.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import com.Hamode.periodpal.utils.AnalyticsUtils
class FirebaseRepository(
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) {
    
    // Authentication
    fun getCurrentUser(): FirebaseUser? = auth.currentUser
    
    fun isUserLoggedIn(): Boolean = auth.currentUser != null
    
    suspend fun signInWithEmail(email: String, password: String): Result<FirebaseUser> {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            val user = result.user!!

            // Log analytics event
            AnalyticsUtils.logUserLogin("email")
            AnalyticsUtils.setUserId(user.uid)

            Result.success(user)
        } catch (e: Exception) {
            AnalyticsUtils.logError("auth_signin_failed", e.message ?: "Unknown error")
            Result.failure(e)
        }
    }
    
    suspend fun signUpWithEmail(email: String, password: String): Result<FirebaseUser> {
        return try {
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            val user = result.user!!

            // Log analytics event
            AnalyticsUtils.logUserSignUp("email")
            AnalyticsUtils.setUserId(user.uid)

            Result.success(user)
        } catch (e: Exception) {
            AnalyticsUtils.logError("auth_signup_failed", e.message ?: "Unknown error")
            Result.failure(e)
        }
    }
    
    fun signOut() {
        auth.signOut()
    }
    
    // User Profile
    suspend fun saveUserProfile(profile: UserProfile): Result<Unit> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            firestore.collection("users")
                .document(userId)
                .set(profile.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getUserProfile(): Result<UserProfile?> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            val document = firestore.collection("users")
                .document(userId)
                .get()
                .await()
            
            if (document.exists()) {
                val profile = UserProfile.fromMap(document.data!!)
                Result.success(profile)
            } else {
                Result.success(null)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Daily Logs
    suspend fun saveDailyLog(log: DailyLog): Result<Unit> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            val dateString = log.date.format(DateTimeFormatter.ISO_LOCAL_DATE)

            firestore.collection("users")
                .document(userId)
                .collection("dailyLogs")
                .document(dateString)
                .set(log.toMap())
                .await()

            // Log analytics events
            if (log.flowIntensity != FlowIntensity.NONE) {
                AnalyticsUtils.logFlowIntensityChange(log.flowIntensity.name)
            }

            log.symptoms.forEach { symptom ->
                AnalyticsUtils.logSymptomLogged(symptom.name)
            }

            if (log.painLevel.level > 0) {
                AnalyticsUtils.logPainLevelLogged(log.painLevel.level)
            }

            log.mood?.let { mood ->
                AnalyticsUtils.logMoodLogged(mood.name)
            }

            Result.success(Unit)
        } catch (e: Exception) {
            AnalyticsUtils.logError("daily_log_save_failed", e.message ?: "Unknown error")
            Result.failure(e)
        }
    }
    
    suspend fun getDailyLog(date: LocalDate): Result<DailyLog?> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            val dateString = date.format(DateTimeFormatter.ISO_LOCAL_DATE)
            
            val document = firestore.collection("users")
                .document(userId)
                .collection("dailyLogs")
                .document(dateString)
                .get()
                .await()
            
            if (document.exists()) {
                val log = dailyLogFromMap(document.data!!)
                Result.success(log)
            } else {
                Result.success(null)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getDailyLogsInRange(startDate: LocalDate, endDate: LocalDate): Result<List<DailyLog>> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            val startString = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
            val endString = endDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
            
            val documents = firestore.collection("users")
                .document(userId)
                .collection("dailyLogs")
                .whereGreaterThanOrEqualTo("dateString", startString)
                .whereLessThanOrEqualTo("dateString", endString)
                .orderBy("dateString")
                .get()
                .await()
            
            val logs = documents.documents.mapNotNull { doc ->
                if (doc.exists()) dailyLogFromMap(doc.data!!) else null
            }
            Result.success(logs)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Health Insights
    suspend fun saveHealthInsight(insight: HealthInsight): Result<Unit> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            
            firestore.collection("users")
                .document(userId)
                .collection("healthInsights")
                .document(insight.id)
                .set(insight.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getHealthInsights(limit: Int = 10): Result<List<HealthInsight>> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            
            val documents = firestore.collection("users")
                .document(userId)
                .collection("healthInsights")
                .orderBy("date", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            val insights = documents.documents.mapNotNull { doc ->
                if (doc.exists()) healthInsightFromMap(doc.data!!) else null
            }
            Result.success(insights)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Cycle Statistics
    suspend fun calculateCycleStatistics(): Result<CycleStatistics> {
        return try {
            val userId = getCurrentUser()?.uid ?: throw Exception("User not logged in")
            
            // Get all daily logs for analysis
            val documents = firestore.collection("users")
                .document(userId)
                .collection("dailyLogs")
                .orderBy("dateString")
                .get()
                .await()
            
            val logs = documents.documents.mapNotNull { doc ->
                if (doc.exists()) dailyLogFromMap(doc.data!!) else null
            }
            
            // Calculate statistics from logs
            val statistics = calculateStatisticsFromLogs(logs)
            Result.success(statistics)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun calculateStatisticsFromLogs(logs: List<DailyLog>): CycleStatistics {
        // Sample calculation - in real app this would be more sophisticated
        val periodDays = logs.filter { it.flowIntensity != FlowIntensity.NONE }
        val cycleLengths = mutableListOf<Int>()
        
        // Find cycle lengths by detecting period starts
        var lastPeriodStart: LocalDate? = null
        for (log in logs.sortedBy { it.date }) {
            if (log.flowIntensity != FlowIntensity.NONE && 
                (lastPeriodStart == null || log.date.isAfter(lastPeriodStart.plusDays(20)))) {
                
                if (lastPeriodStart != null) {
                    val cycleLength = log.date.toEpochDay() - lastPeriodStart.toEpochDay()
                    cycleLengths.add(cycleLength.toInt())
                }
                lastPeriodStart = log.date
            }
        }
        
        val avgCycleLength = if (cycleLengths.isNotEmpty()) cycleLengths.average() else 28.0
        val avgPeriodLength = 5.0 // Sample value
        val cycleVariability = if (cycleLengths.size > 1) {
            cycleLengths.map { kotlin.math.abs(it - avgCycleLength) }.average()
        } else 0.0
        
        val symptoms = logs.flatMap { it.symptoms }.groupingBy { it }.eachCount()
        val mostCommonSymptoms = symptoms.toList().sortedByDescending { it.second }.take(5).map { it.first }
        
        val avgPainLevel = logs.map { it.painLevel.level }.average()
        val moodTrends = logs.mapNotNull { it.mood }.groupingBy { it }.eachCount()
        
        return CycleStatistics(
            averageCycleLength = avgCycleLength,
            averagePeriodLength = avgPeriodLength,
            cycleVariability = cycleVariability,
            totalCycles = cycleLengths.size,
            longestCycle = cycleLengths.maxOrNull() ?: 28,
            shortestCycle = cycleLengths.minOrNull() ?: 28,
            mostCommonSymptoms = mostCommonSymptoms,
            averagePainLevel = avgPainLevel,
            moodTrends = moodTrends
        )
    }
}
