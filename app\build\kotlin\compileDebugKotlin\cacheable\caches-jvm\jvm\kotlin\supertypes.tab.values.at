/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Application kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity android.app.Application kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity android.app.Application kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity