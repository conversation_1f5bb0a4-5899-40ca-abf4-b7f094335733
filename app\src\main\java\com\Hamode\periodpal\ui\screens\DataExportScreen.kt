package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.ProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataExportScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    profileViewModel: ProfileViewModel = viewModel()
) {
    val uiState by profileViewModel.uiState.collectAsState()
    var selectedFormat by remember { mutableStateOf("JSON") }
    var includeProfile by remember { mutableStateOf(true) }
    var includeLogs by remember { mutableStateOf(true) }
    var includeStatistics by remember { mutableStateOf(true) }
    var dateRange by remember { mutableStateOf("All Time") }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = RosePink40
                )
            }
            Text(
                text = "Data Export",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.weight(1f)
            )
        }

        // Export Format Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Export Format",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    FormatChip(
                        text = "JSON",
                        isSelected = selectedFormat == "JSON",
                        onClick = { selectedFormat = "JSON" },
                        modifier = Modifier.weight(1f)
                    )
                    FormatChip(
                        text = "CSV",
                        isSelected = selectedFormat == "CSV",
                        onClick = { selectedFormat = "CSV" },
                        modifier = Modifier.weight(1f)
                    )
                }

                Text(
                    text = when (selectedFormat) {
                        "JSON" -> "Structured data format, best for backup and data migration"
                        "CSV" -> "Spreadsheet format, easy to open in Excel or Google Sheets"
                        else -> ""
                    },
                    fontSize = 14.sp,
                    color = DarkGray
                )
            }
        }

        // Data Selection Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Data to Include",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                DataSelectionItem(
                    title = "Profile Information",
                    description = "Personal details and cycle settings",
                    icon = Icons.Default.Person,
                    iconColor = RosePink40,
                    isSelected = includeProfile,
                    onToggle = { includeProfile = it }
                )

                DataSelectionItem(
                    title = "Daily Logs",
                    description = "Period tracking, symptoms, and health data",
                    icon = Icons.Default.Assignment,
                    iconColor = Color(0xFF2196F3),
                    isSelected = includeLogs,
                    onToggle = { includeLogs = it }
                )

                DataSelectionItem(
                    title = "Statistics & Insights",
                    description = "Cycle patterns and health analytics",
                    icon = Icons.Default.Analytics,
                    iconColor = FertileGreen,
                    isSelected = includeStatistics,
                    onToggle = { includeStatistics = it }
                )
            }
        }

        // Date Range Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Date Range",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    DateRangeChip(
                        text = "Last 3 Months",
                        isSelected = dateRange == "Last 3 Months",
                        onClick = { dateRange = "Last 3 Months" },
                        modifier = Modifier.weight(1f)
                    )
                    DateRangeChip(
                        text = "Last Year",
                        isSelected = dateRange == "Last Year",
                        onClick = { dateRange = "Last Year" },
                        modifier = Modifier.weight(1f)
                    )
                    DateRangeChip(
                        text = "All Time",
                        isSelected = dateRange == "All Time",
                        onClick = { dateRange = "All Time" },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }

        // Privacy Notice Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFFF3E0)
            )
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.Top
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = "Privacy",
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = "Privacy Notice",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFFE65100)
                    )
                    Text(
                        text = "Your exported data will be saved locally on your device. Please ensure you store it securely and only share with trusted healthcare providers.",
                        fontSize = 14.sp,
                        color = Color(0xFFBF360C)
                    )
                }
            }
        }

        // Error Message
        uiState.errorMessage?.let { errorMessage ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = ErrorRed.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = errorMessage,
                    color = ErrorRed,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        // Success Message
        if (uiState.showSuccessMessage) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = FertileGreen.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = "Data exported successfully! Check your downloads folder.",
                    color = FertileGreen,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        // Export Button
        Button(
            onClick = {
                profileViewModel.exportUserData()
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = RosePink40
            ),
            shape = RoundedCornerShape(12.dp),
            enabled = !uiState.isLoading && (includeProfile || includeLogs || includeStatistics)
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = "Export",
                        tint = Color.White
                    )
                    Text(
                        text = "Export Data",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        // Bottom spacing
        Spacer(modifier = Modifier.height(80.dp))
    }
}

@Composable
private fun FormatChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = { Text(text) },
        selected = isSelected,
        modifier = modifier,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = RosePink40,
            selectedLabelColor = Color.White
        )
    )
}

@Composable
private fun DateRangeChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = { Text(text, fontSize = 12.sp) },
        selected = isSelected,
        modifier = modifier,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = RosePink40,
            selectedLabelColor = Color.White
        )
    )
}

@Composable
private fun DataSelectionItem(
    title: String,
    description: String,
    icon: ImageVector,
    iconColor: Color,
    isSelected: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = iconColor,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(16.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = description,
                fontSize = 14.sp,
                color = DarkGray
            )
        }
        Checkbox(
            checked = isSelected,
            onCheckedChange = onToggle,
            colors = CheckboxDefaults.colors(
                checkedColor = iconColor
            )
        )
    }
}
