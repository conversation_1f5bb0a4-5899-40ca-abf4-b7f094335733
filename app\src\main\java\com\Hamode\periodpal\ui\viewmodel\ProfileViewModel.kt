package com.Hamode.periodpal.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.Hamode.periodpal.data.models.UserProfile
import com.Hamode.periodpal.data.models.CycleStatistics
import com.Hamode.periodpal.data.models.toMap
import com.Hamode.periodpal.data.repository.FirebaseRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class ProfileViewModel(
    private val repository: FirebaseRepository = FirebaseRepository(
        FirebaseAuth.getInstance(),
        FirebaseFirestore.getInstance()
    )
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    private val _userProfile = MutableStateFlow<UserProfile?>(null)
    val userProfile: StateFlow<UserProfile?> = _userProfile.asStateFlow()

    private val _cycleStatistics = MutableStateFlow<CycleStatistics?>(null)
    val cycleStatistics: StateFlow<CycleStatistics?> = _cycleStatistics.asStateFlow()

    init {
        loadUserProfile()
        loadCycleStatistics()
    }

    fun loadUserProfile() {
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
        
        viewModelScope.launch {
            repository.getUserProfile()
                .onSuccess { profile ->
                    _userProfile.value = profile
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = null
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Failed to load profile: ${exception.message}"
                    )
                }
        }
    }

    fun loadCycleStatistics() {
        viewModelScope.launch {
            repository.calculateCycleStatistics()
                .onSuccess { statistics ->
                    _cycleStatistics.value = statistics
                }
                .onFailure { exception ->
                    // Don't show error for statistics as it's not critical
                    println("Failed to load cycle statistics: ${exception.message}")
                }
        }
    }

    fun updateUserProfile(profile: UserProfile) {
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
        
        viewModelScope.launch {
            val updatedProfile = profile.copy(updatedAt = LocalDate.now())
            repository.saveUserProfile(updatedProfile)
                .onSuccess {
                    _userProfile.value = updatedProfile
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = null,
                        showSuccessMessage = true
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Failed to update profile: ${exception.message}"
                    )
                }
        }
    }

    fun signOut() {
        repository.signOut()
    }

    fun clearSuccessMessage() {
        _uiState.value = _uiState.value.copy(showSuccessMessage = false)
    }

    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    // Helper functions for profile statistics
    fun getNextPeriodDays(): Int? {
        val profile = _userProfile.value ?: return null
        val lastPeriodStart = profile.lastPeriodStart ?: return null
        val cycleLength = profile.averageCycleLength
        
        val nextPeriodDate = lastPeriodStart.plusDays(cycleLength.toLong())
        val today = LocalDate.now()
        
        return if (nextPeriodDate.isAfter(today)) {
            ChronoUnit.DAYS.between(today, nextPeriodDate).toInt()
        } else {
            // Period is overdue
            -ChronoUnit.DAYS.between(nextPeriodDate, today).toInt()
        }
    }

    fun getCycleDayNumber(): Int? {
        val profile = _userProfile.value ?: return null
        val lastPeriodStart = profile.lastPeriodStart ?: return null
        val today = LocalDate.now()

        val daysSinceLastPeriod = ChronoUnit.DAYS.between(lastPeriodStart, today).toInt()
        return if (daysSinceLastPeriod >= 0) daysSinceLastPeriod + 1 else null
    }

    fun updateLastPeriodStart(date: LocalDate) {
        val currentProfile = _userProfile.value ?: return
        val updatedProfile = currentProfile.copy(
            lastPeriodStart = date,
            updatedAt = LocalDate.now()
        )
        updateUserProfile(updatedProfile)
    }

    fun refreshData() {
        loadUserProfile()
        loadCycleStatistics()
    }

    fun exportUserData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

                // Get user profile
                val profileResult = repository.getUserProfile()

                // Get daily logs (last 365 days)
                val endDate = LocalDate.now()
                val startDate = endDate.minusDays(365)
                val logsResult = repository.getDailyLogsInRange(startDate, endDate)

                // Get cycle statistics
                val statsResult = repository.calculateCycleStatistics()

                // Create export data
                val exportData = mutableMapOf<String, Any>()

                profileResult.onSuccess { profile ->
                    profile?.let { exportData["profile"] = it.toMap() }
                }

                logsResult.onSuccess { logs: List<com.Hamode.periodpal.data.models.DailyLog> ->
                    exportData["dailyLogs"] = logs.map { it.toMap() }
                }

                statsResult.onSuccess { stats ->
                    exportData["statistics"] = mapOf(
                        "averageCycleLength" to stats.averageCycleLength,
                        "averagePeriodLength" to stats.averagePeriodLength,
                        "cycleVariability" to stats.cycleVariability,
                        "totalCycles" to stats.totalCycles,
                        "regularityDescription" to stats.getRegularityDescription()
                    )
                }

                exportData["exportDate"] = LocalDate.now().toString()
                exportData["appVersion"] = "1.0.0"

                // TODO: Save to file or share
                // For now, just show success message
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showSuccessMessage = true
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to export data: ${e.message}"
                )
            }
        }
    }
}

data class ProfileUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val showSuccessMessage: Boolean = false
)
