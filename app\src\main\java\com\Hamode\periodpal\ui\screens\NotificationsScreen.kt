package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.ProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationsScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    profileViewModel: ProfileViewModel = viewModel()
) {
    val userProfile by profileViewModel.userProfile.collectAsState()
    val uiState by profileViewModel.uiState.collectAsState()

    var periodReminders by remember { mutableStateOf(true) }
    var ovulationReminders by remember { mutableStateOf(true) }
    var medicationReminders by remember { mutableStateOf(false) }
    var symptomReminders by remember { mutableStateOf(true) }
    var healthInsights by remember { mutableStateOf(true) }
    var weeklyReports by remember { mutableStateOf(false) }
    var emergencyAlerts by remember { mutableStateOf(true) }

    // Initialize with current profile data
    LaunchedEffect(userProfile) {
        userProfile?.let { profile ->
            periodReminders = profile.notifications.periodReminders
            ovulationReminders = profile.notifications.ovulationReminders
            medicationReminders = profile.notifications.medicationReminders
            symptomReminders = profile.notifications.symptomReminders
            healthInsights = profile.notifications.healthInsights
            weeklyReports = profile.notifications.weeklyReports
            emergencyAlerts = profile.notifications.emergencyAlerts
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = RosePink40
                )
            }
            Text(
                text = "Notifications",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.weight(1f)
            )
        }

        // Cycle Notifications Card
        NotificationCard(
            title = "Cycle Notifications",
            items = listOf(
                NotificationItem(
                    title = "Period Reminders",
                    description = "Get notified 2 days before your period starts",
                    icon = Icons.Default.Bloodtype,
                    iconColor = PeriodRed,
                    isEnabled = periodReminders,
                    onToggle = { periodReminders = it }
                ),
                NotificationItem(
                    title = "Ovulation Reminders",
                    description = "Get notified during your fertile window",
                    icon = Icons.Default.Favorite,
                    iconColor = FertileGreen,
                    isEnabled = ovulationReminders,
                    onToggle = { ovulationReminders = it }
                )
            )
        )

        // Health Notifications Card
        NotificationCard(
            title = "Health Notifications",
            items = listOf(
                NotificationItem(
                    title = "Symptom Reminders",
                    description = "Daily reminders to log your symptoms",
                    icon = Icons.Default.HealthAndSafety,
                    iconColor = RosePink40,
                    isEnabled = symptomReminders,
                    onToggle = { symptomReminders = it }
                ),
                NotificationItem(
                    title = "Medication Reminders",
                    description = "Reminders for birth control and supplements",
                    icon = Icons.Default.Medication,
                    iconColor = Color(0xFF4CAF50),
                    isEnabled = medicationReminders,
                    onToggle = { medicationReminders = it }
                ),
                NotificationItem(
                    title = "Health Insights",
                    description = "Weekly insights about your cycle patterns",
                    icon = Icons.Default.Analytics,
                    iconColor = Color(0xFF2196F3),
                    isEnabled = healthInsights,
                    onToggle = { healthInsights = it }
                )
            )
        )

        // Reports & Alerts Card
        NotificationCard(
            title = "Reports & Alerts",
            items = listOf(
                NotificationItem(
                    title = "Weekly Reports",
                    description = "Summary of your week's health data",
                    icon = Icons.Default.Assessment,
                    iconColor = Color(0xFF9C27B0),
                    isEnabled = weeklyReports,
                    onToggle = { weeklyReports = it }
                ),
                NotificationItem(
                    title = "Emergency Alerts",
                    description = "Important health alerts and warnings",
                    icon = Icons.Default.Warning,
                    iconColor = ErrorRed,
                    isEnabled = emergencyAlerts,
                    onToggle = { emergencyAlerts = it }
                )
            )
        )

        // Error Message
        uiState.errorMessage?.let { errorMessage ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = ErrorRed.copy(alpha = 0.1f)
                )
            ) {
                Text(
                    text = errorMessage,
                    color = ErrorRed,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        // Save Button
        Button(
            onClick = {
                val currentProfile = userProfile ?: return@Button
                val updatedProfile = currentProfile.copy(
                    notifications = currentProfile.notifications.copy(
                        periodReminders = periodReminders,
                        ovulationReminders = ovulationReminders,
                        medicationReminders = medicationReminders,
                        symptomReminders = symptomReminders,
                        healthInsights = healthInsights,
                        weeklyReports = weeklyReports,
                        emergencyAlerts = emergencyAlerts
                    )
                )
                profileViewModel.updateUserProfile(updatedProfile)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = RosePink40
            ),
            shape = RoundedCornerShape(12.dp),
            enabled = !uiState.isLoading
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "Save",
                        tint = Color.White
                    )
                    Text(
                        text = "Save Preferences",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        // Bottom spacing
        Spacer(modifier = Modifier.height(80.dp))
    }
}

@Composable
private fun NotificationCard(
    title: String,
    items: List<NotificationItem>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            items.forEach { item ->
                NotificationItemRow(item = item)
            }
        }
    }
}

@Composable
private fun NotificationItemRow(
    item: NotificationItem,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.title,
            tint = item.iconColor,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(16.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = item.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = item.description,
                fontSize = 14.sp,
                color = DarkGray
            )
        }
        Switch(
            checked = item.isEnabled,
            onCheckedChange = item.onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = item.iconColor
            )
        )
    }
}

data class NotificationItem(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val iconColor: Color,
    val isEnabled: Boolean,
    val onToggle: (Boolean) -> Unit
)
