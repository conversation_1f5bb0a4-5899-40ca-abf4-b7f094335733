package com.Hamode.periodpal.ui.navigation

/**
 * Main navigation destinations
 */
enum class NavigationDestination(val displayName: String) {
    HOME("Home"),
    CALENDAR("Calendar"),
    HEALTH_LOGS("Health Logs"),
    INSIGHTS("Insights"),
    PROFILE("Profile")
}

/**
 * Secondary navigation destinations (overlays/modals)
 */
enum class SecondaryDestination(val displayName: String) {
    SYMPTOM_LOGGING("Log Symptoms"),
    CYCLE_HISTORY("Cycle History"),
    EDIT_PROFILE("Edit Profile"),
    CYCLE_SETTINGS("Cycle Settings"),
    NOTIFICATIONS("Notifications"),
    DATA_EXPORT("Data Export"),
    PRIVACY_SETTINGS("Privacy Settings"),
    EMERGENCY_CONTACTS("Emergency Contacts"),
    HELP_FAQ("Help & FAQ"),
    CONTACT_SUPPORT("Contact Support"),
    ABOUT("About PeriodPal")
}
