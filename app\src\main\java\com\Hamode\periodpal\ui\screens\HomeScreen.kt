package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.utils.AnalyticsUtils
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun HomeScreen(
    onNavigateToCalendar: () -> Unit = {},
    onNavigateToLogs: () -> Unit = {},
    onNavigateToInsights: () -> Unit = {},
    onNavigateToSettings: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    var currentDate by remember { mutableStateOf(LocalDate.now()) }
    var currentCycleDay by remember { mutableStateOf(15) }
    var currentPhase by remember { mutableStateOf(CyclePhase.FOLLICULAR) }
    var flowIntensity by remember { mutableStateOf(FlowIntensity.NONE) }

    // Log screen view
    LaunchedEffect(Unit) {
        AnalyticsUtils.logScreenView("home_screen")
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        SoftPink80.copy(alpha = 0.1f)
                    )
                )
            )
    ) {
        // Header Section
        HeaderSection(
            currentDate = currentDate,
            cycleDay = currentCycleDay,
            phase = currentPhase,
            onNotificationClick = { /* Handle notifications */ },
            onSettingsClick = onNavigateToSettings,
            modifier = Modifier.fillMaxWidth()
        )
        
        // Main Content - Scrollable
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Cycle Overview Card
            CycleOverviewCard(
                cycleDay = currentCycleDay,
                phase = currentPhase,
                nextPeriodDays = 14,
                modifier = Modifier.fillMaxWidth()
            )
            
            // Quick Action Buttons
            QuickActionButtons(
                onStartPeriod = {
                    flowIntensity = FlowIntensity.LIGHT
                    currentPhase = CyclePhase.MENSTRUAL
                    AnalyticsUtils.logPeriodStart()
                },
                onEndPeriod = {
                    flowIntensity = FlowIntensity.NONE
                    AnalyticsUtils.logPeriodEnd(5) // Sample duration
                },
                onLogSymptoms = {
                    AnalyticsUtils.logScreenView("symptoms_log")
                    onNavigateToLogs()
                },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Today's Logging Section
            TodayLoggingSection(
                selectedDate = currentDate,
                flowIntensity = flowIntensity,
                onFlowIntensityChanged = { flowIntensity = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Health Insights Preview
            HealthInsightsPreview(
                onViewAllInsights = onNavigateToInsights,
                modifier = Modifier.fillMaxWidth()
            )
            
            // Bottom spacing for navigation
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun HeaderSection(
    currentDate: LocalDate,
    cycleDay: Int,
    phase: CyclePhase,
    onNotificationClick: () -> Unit,
    onSettingsClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = RosePink40
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Hello! 🌸",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = White
                )
                Text(
                    text = currentDate.format(DateTimeFormatter.ofPattern("EEEE, MMM dd")),
                    fontSize = 14.sp,
                    color = White.copy(alpha = 0.9f)
                )
                Text(
                    text = "Day $cycleDay • ${phase.displayName}",
                    fontSize = 12.sp,
                    color = White.copy(alpha = 0.8f)
                )
            }
            
            Row {
                IconButton(onClick = onNotificationClick) {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = "Notifications",
                        tint = White
                    )
                }
                IconButton(onClick = onSettingsClick) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "Settings",
                        tint = White
                    )
                }
            }
        }
    }
}

@Composable
private fun CycleOverviewCard(
    cycleDay: Int,
    phase: CyclePhase,
    nextPeriodDays: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Cycle Overview",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                OverviewItem(
                    title = "Current Day",
                    value = cycleDay.toString(),
                    icon = Icons.Default.CalendarToday,
                    color = PeriodPalThemeColors.getPhaseColor(phase)
                )
                
                OverviewItem(
                    title = "Phase",
                    value = phase.displayName,
                    icon = Icons.Default.Circle,
                    color = PeriodPalThemeColors.getPhaseColor(phase)
                )
                
                OverviewItem(
                    title = "Next Period",
                    value = "$nextPeriodDays days",
                    icon = Icons.Default.Schedule,
                    color = PeriodRed
                )
            }
        }
    }
}

@Composable
private fun OverviewItem(
    title: String,
    value: String,
    icon: ImageVector,
    color: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = title,
            fontSize = 12.sp,
            color = DarkGray,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun QuickActionButtons(
    onStartPeriod: () -> Unit,
    onEndPeriod: () -> Unit,
    onLogSymptoms: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick Actions",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                QuickActionButton(
                    text = "Start Period",
                    icon = Icons.Default.PlayArrow,
                    onClick = onStartPeriod,
                    backgroundColor = PeriodRed,
                    modifier = Modifier.weight(1f)
                )

                QuickActionButton(
                    text = "End Period",
                    icon = Icons.Default.Stop,
                    onClick = onEndPeriod,
                    backgroundColor = DarkGray,
                    modifier = Modifier.weight(1f)
                )

                QuickActionButton(
                    text = "Log Symptoms",
                    icon = Icons.Default.Add,
                    onClick = onLogSymptoms,
                    backgroundColor = RosePink40,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun QuickActionButton(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    backgroundColor: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier.height(56.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = White,
                modifier = Modifier.size(16.dp)
            )
            Text(
                text = text,
                color = White,
                fontSize = 10.sp,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun TodayLoggingSection(
    selectedDate: LocalDate,
    flowIntensity: FlowIntensity,
    onFlowIntensityChanged: (FlowIntensity) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Today's Log",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Flow Intensity",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = DarkGray
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                FlowIntensity.values().forEach { intensity ->
                    FlowIntensityButton(
                        intensity = intensity,
                        isSelected = flowIntensity == intensity,
                        onClick = { onFlowIntensityChanged(intensity) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun FlowIntensityButton(
    intensity: FlowIntensity,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (isSelected) {
        PeriodPalThemeColors.getFlowColor(intensity)
    } else {
        LightGray
    }

    Button(
        onClick = onClick,
        modifier = modifier.height(40.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(
            text = intensity.level.toString(),
            color = if (isSelected) White else DarkGray,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun HealthInsightsPreview(
    onViewAllInsights: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Health Insights",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                TextButton(onClick = onViewAllInsights) {
                    Text(
                        text = "View All",
                        color = RosePink40,
                        fontSize = 12.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Sample insight
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.TrendingUp,
                    contentDescription = "Insight",
                    tint = SuccessGreen,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Your cycles are very regular! Keep up the good tracking.",
                    fontSize = 14.sp,
                    color = DarkGray
                )
            }
        }
    }
}
