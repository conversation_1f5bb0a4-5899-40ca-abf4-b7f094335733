package com.Hamode.periodpal.utils

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore

object FirebaseTestUtils {
    private const val TAG = "FirebaseTest"

    fun testFirebaseConnection() {
        try {
            val auth = FirebaseAuth.getInstance()
            val firestore = FirebaseFirestore.getInstance()

            Log.d(TAG, "=== Firebase Connection Test ===")
            Log.d(TAG, "Firebase Auth instance: ${auth.app.name}")
            Log.d(TAG, "Firebase Firestore instance: ${firestore.app.name}")
            Log.d(TAG, "Current user: ${auth.currentUser?.uid ?: "No user logged in"}")
            Log.d(TAG, "Firebase project ID: ${firestore.app.options.projectId}")

            // Test Firestore connection with more detailed logging
            val testData = mapOf(
                "timestamp" to System.currentTimeMillis(),
                "testMessage" to "PeriodPal Firebase connection test",
                "version" to "1.0.0"
            )

            firestore.collection("test").document("connection")
                .set(testData)
                .addOnSuccessListener {
                    Log.d(TAG, "✅ Firestore connection test successful!")
                    Log.d(TAG, "✅ Data written to Firestore successfully")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "❌ Firestore connection test failed", e)
                    Log.e(TAG, "❌ Error details: ${e.message}")
                }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Firebase initialization error", e)
            Log.e(TAG, "❌ Error details: ${e.message}")
        }
    }

    fun testFirebaseRepository() {
        try {
            Log.d(TAG, "=== Firebase Repository Test ===")
            val repository = com.Hamode.periodpal.data.repository.FirebaseRepository(
                FirebaseAuth.getInstance(),
                FirebaseFirestore.getInstance()
            )

            Log.d(TAG, "Repository created successfully")
            Log.d(TAG, "User logged in: ${repository.isUserLoggedIn()}")
            Log.d(TAG, "Current user: ${repository.getCurrentUser()?.uid ?: "None"}")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Repository test failed", e)
        }
    }
}
